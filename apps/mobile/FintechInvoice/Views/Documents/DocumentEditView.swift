import SwiftUI

struct DocumentEditView: View {
    let document: Document
    @Environment(\.dismiss) private var dismiss
    @StateObject private var documentViewModel = DocumentViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var isLoading = true
    @State private var errorMessage: String?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if isLoading {
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("טוען מסמך...")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if let errorMessage = errorMessage {
                    VStack(spacing: .spacing4) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 40))
                            .foregroundColor(.destructive)
                        
                        Text("שגיאה בטעינת המסמך")
                            .font(.hebrewHeading.weight(.semibold))
                            .foregroundColor(.cardForeground)
                        
                        Text(errorMessage)
                            .font(.hebrewBody)
                            .foregroundColor(.mutedForeground)
                            .multilineTextAlignment(.center)
                        
                        Button("נסה שוב") {
                            loadDocumentForEditing()
                        }
                        .buttonStyle(CosmicPrimaryButtonStyle())
                    }
                    .padding(.spacing6)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // Progress Bar
                    ProgressBarView(
                        currentStep: documentViewModel.currentStep.stepNumber,
                        totalSteps: DocumentCreationStep.totalSteps
                    )
                    .padding(.horizontal, .spacing4)
                    .padding(.top, .spacing2)
                    
                    // Content
                    ScrollView {
                        VStack(spacing: .spacing6) {
                            currentStepView
                        }
                        .padding(.spacing4)
                    }
                    .background(Color.background)
                    
                    // Navigation Buttons
                    NavigationButtonsView(documentViewModel: documentViewModel)
                }
            }
            .background(Color.background)
            .navigationTitle("עריכת מסמך")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .onAppear {
            setupDocumentForEditing()
            loadDocumentForEditing()
        }
        .sheet(isPresented: $documentViewModel.showingCreateCustomer) {
            CreateCustomerView(documentViewModel: documentViewModel)
        }
        .sheet(isPresented: $documentViewModel.showingCreateProduct) {
            CreateProductView(documentViewModel: documentViewModel)
        }
        .sheet(isPresented: $documentViewModel.showingDocumentSuccess) {
            DocumentSuccessView(documentViewModel: documentViewModel)
        }
        .onChange(of: documentViewModel.showingDocumentSuccess) { isShowing in
            if !isShowing && documentViewModel.createdDocument != nil {
                // Add a small delay to ensure notifications are processed
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    dismiss()
                }
            }
        }
        .alert("שגיאה", isPresented: .constant(documentViewModel.errorMessage != nil)) {
            Button("אישור") {
                documentViewModel.errorMessage = nil
            }
        } message: {
            Text(documentViewModel.errorMessage ?? "")
        }
        .alert("הצלחה", isPresented: .constant(documentViewModel.successMessage != nil)) {
            Button("אישור") {
                documentViewModel.successMessage = nil
                // Add a small delay to ensure notifications are processed
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    dismiss()
                }
            }
        } message: {
            Text(documentViewModel.successMessage ?? "")
        }
    }
    
    @ViewBuilder
    private var currentStepView: some View {
        switch documentViewModel.currentStep {
        case .documentType:
            DocumentTypeSelectionView(documentViewModel: documentViewModel)
        case .customerSelection:
            CustomerSelectionView(documentViewModel: documentViewModel)
        case .productSelection:
            ProductSelectionView(documentViewModel: documentViewModel)
        case .summary:
            DocumentSummaryView(documentViewModel: documentViewModel)
        }
    }
    
    private func setupDocumentForEditing() {
        // Set the company ID
        if let selectedCompany = authViewModel.selectedCompany {
            documentViewModel.setCompanyId(selectedCompany.id)
        }

        // Setup edit mode in the view model
        documentViewModel.setupForEditing(document: document)
    }
    
    private func loadDocumentForEditing() {
        isLoading = true
        errorMessage = nil

        Task {
            // Load customers and products first
            documentViewModel.loadCustomers()
            documentViewModel.loadProducts()

            // Load document data for editing (items and customer)
            await documentViewModel.loadDocumentDataForEditing()

            await MainActor.run {
                isLoading = false
            }
        }
    }
    

}

// MARK: - Preview
#Preview {
    // Create a mock Document using JSON decoding since Document only has init(from decoder:)
    let mockDocumentJSON = """
    {
        "id": "test-id",
        "company_id": "test-company",
        "document_type": "tax_invoice",
        "document_number": "INV-2025-001",
        "customer_id": "test-customer",
        "issue_date": "2025-01-15",
        "due_date": "2025-02-15",
        "currency": "ILS",
        "subtotal": 1000.0,
        "vat_amount": 180.0,
        "total_amount": 1180.0,
        "status": "draft",
        "ita_allocation_number": null,
        "ita_allocation_date": null,
        "ita_submission_attempts": 0,
        "ita_last_error": null,
        "parent_document_id": null,
        "notes": "Test notes",
        "template_id": "default",
        "pdf_url": null,
        "sent_at": null,
        "sent_via": null,
        "created_by": "test-user",
        "created_at": "2025-01-15T10:00:00Z",
        "updated_at": "2025-01-15T10:00:00Z"
    }
    """

    let mockDocument = try! JSONDecoder().decode(Document.self, from: mockDocumentJSON.data(using: .utf8)!)

    return DocumentEditView(document: mockDocument)
        .environmentObject(AuthViewModel())
}
