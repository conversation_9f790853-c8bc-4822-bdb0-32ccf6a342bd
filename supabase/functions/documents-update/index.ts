import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  checkUserRole,
  logAudit
} from '../_shared/utils.ts';

interface UpdateDocumentRequest {
  document_id: string;
  document_type?: string;
  customer_id?: string;
  issue_date?: string;
  due_date?: string;
  currency?: string;
  items?: {
    product_id?: string;
    description_hebrew: string;
    description_english?: string;
    quantity: number;
    unit_price: number;
    vat_rate: number;
    discount_percent?: number;
  }[];
  notes?: string;
  template_id?: string;
  status?: 'draft' | 'pending_allocation';
}

interface DocumentCalculations {
  subtotal: number;
  vat_amount: number;
  total_amount: number;
  items: Array<{
    product_id?: string;
    line_number: number;
    description_hebrew: string;
    description_english?: string;
    quantity: number;
    unit_price: number;
    discount_percent: number;
    vat_rate: number;
    line_total: number;
    vat_amount: number;
    total_with_vat: number;
  }>;
}

function calculateDocumentTotals(items: UpdateDocumentRequest['items']): DocumentCalculations {
  if (!items || items.length === 0) {
    return {
      subtotal: 0,
      vat_amount: 0,
      total_amount: 0,
      items: []
    };
  }

  let subtotal = 0;
  let totalVat = 0;
  const calculatedItems = items.map((item, index) => {
    const discountPercent = item.discount_percent || 0;
    const vatRate = item.vat_rate || 18;
    
    const lineTotal = item.quantity * item.unit_price;
    const discountAmount = lineTotal * (discountPercent / 100);
    const lineAfterDiscount = lineTotal - discountAmount;
    const vatAmount = lineAfterDiscount * (vatRate / 100);
    const totalWithVat = lineAfterDiscount + vatAmount;
    
    subtotal += lineAfterDiscount;
    totalVat += vatAmount;
    
    return {
      product_id: item.product_id,
      line_number: index + 1,
      description_hebrew: item.description_hebrew,
      description_english: item.description_english,
      quantity: item.quantity,
      unit_price: item.unit_price,
      discount_percent: discountPercent,
      vat_rate: vatRate,
      line_total: lineAfterDiscount,
      vat_amount: vatAmount,
      total_with_vat: totalWithVat
    };
  });

  return {
    subtotal,
    vat_amount: totalVat,
    total_amount: subtotal + totalVat,
    items: calculatedItems
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const body: UpdateDocumentRequest = await req.json();
    console.log('📝 Update document request:', body);

    // Validate required fields
    const validationErrors: string[] = [];
    
    if (!body.document_id) {
      validationErrors.push('document_id is required');
    }

    // Validate items if provided
    if (body.items) {
      body.items.forEach((item, index) => {
        if (!item.description_hebrew) {
          validationErrors.push(`Item ${index + 1}: description_hebrew is required`);
        }
        if (item.quantity === undefined || item.quantity <= 0) {
          validationErrors.push(`Item ${index + 1}: quantity must be greater than 0`);
        }
        if (item.unit_price === undefined || item.unit_price < 0) {
          validationErrors.push(`Item ${index + 1}: unit_price must be non-negative`);
        }
        if (item.vat_rate === undefined || item.vat_rate < 0) {
          item.vat_rate = 18; // Default Israeli VAT rate
        }
        if (item.discount_percent === undefined || item.discount_percent < 0) {
          item.discount_percent = 0;
        }
      });
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    // Get user from JWT
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return errorResponse('Authorization header required', 401);
    }

    const supabase = createSupabaseClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (userError || !user) {
      console.error('Auth error:', userError);
      return errorResponse('Invalid authentication', 401);
    }

    // Get existing document to verify ownership and get company_id
    const { data: existingDocument, error: docError } = await supabase
      .from('documents')
      .select('*')
      .eq('id', body.document_id)
      .single();

    if (docError || !existingDocument) {
      console.error('Document fetch error:', docError);
      return errorResponse('Document not found', 404);
    }

    // Check user access to company
    await checkUserRole(user.id, existingDocument.company_id, ['admin', 'user', 'accountant']);

    // Verify customer exists and belongs to company if customer_id is being updated
    if (body.customer_id && body.customer_id !== existingDocument.customer_id) {
      const { data: customer, error: customerError } = await supabase
        .from('customers')
        .select('*')
        .eq('id', body.customer_id)
        .eq('company_id', existingDocument.company_id)
        .single();

      if (customerError || !customer) {
        console.error('Customer verification error:', customerError);
        return errorResponse('Customer not found or does not belong to company', 400);
      }
    }

    // Calculate totals if items are provided
    let calculations: DocumentCalculations | null = null;
    if (body.items) {
      calculations = calculateDocumentTotals(body.items);
    }

    // Prepare document update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Only update fields that are provided
    if (body.document_type) updateData.document_type = body.document_type;
    if (body.customer_id) updateData.customer_id = body.customer_id;
    if (body.issue_date) updateData.issue_date = body.issue_date;
    if (body.due_date !== undefined) updateData.due_date = body.due_date;
    if (body.currency) updateData.currency = body.currency;
    if (body.notes !== undefined) updateData.notes = body.notes;
    if (body.template_id) updateData.template_id = body.template_id;
    if (body.status) updateData.status = body.status;

    // Update calculated totals if items were provided
    if (calculations) {
      updateData.subtotal = calculations.subtotal;
      updateData.vat_amount = calculations.vat_amount;
      updateData.total_amount = calculations.total_amount;
    }

    // Update document
    const { data: updatedDocument, error: updateError } = await supabase
      .from('documents')
      .update(updateData)
      .eq('id', body.document_id)
      .select()
      .single();

    if (updateError) {
      console.error('Document update error:', updateError);
      return errorResponse('Failed to update document', 500);
    }

    // Update document items if provided
    if (body.items && calculations) {
      // Delete existing items
      const { error: deleteError } = await supabase
        .from('document_items')
        .delete()
        .eq('document_id', body.document_id);

      if (deleteError) {
        console.error('Document items delete error:', deleteError);
        return errorResponse('Failed to update document items', 500);
      }

      // Insert new items
      const itemsToInsert = calculations.items.map(item => ({
        document_id: body.document_id,
        product_id: item.product_id,
        line_number: item.line_number,
        description_hebrew: item.description_hebrew,
        description_english: item.description_english,
        quantity: item.quantity,
        unit_price: item.unit_price,
        currency: body.currency || existingDocument.currency,
        discount_percent: item.discount_percent || 0,
        vat_rate: item.vat_rate || 18,
        line_total: item.line_total,
        vat_amount: item.vat_amount,
        total_with_vat: item.total_with_vat,
      }));

      const { error: itemsError } = await supabase
        .from('document_items')
        .insert(itemsToInsert);

      if (itemsError) {
        console.error('Document items insert error:', itemsError);
        return errorResponse('Failed to update document items', 500);
      }
    }

    // Log audit
    await logAudit(
      existingDocument.company_id,
      user.id,
      'update',
      'document',
      body.document_id,
      existingDocument,
      updatedDocument,
      req
    );

    console.log('✅ Document updated successfully:', updatedDocument.id);
    return successResponse({
      document: updatedDocument,
      ita_submission: null
    });

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return errorResponse('Internal server error', 500);
  }
});
