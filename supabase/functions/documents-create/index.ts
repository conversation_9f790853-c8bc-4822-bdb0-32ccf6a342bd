import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  calculateDocumentTotals,
  logAudit,
} from '../_shared/utils.ts';
import { CreateDocumentRequest, ValidationError } from '../_shared/types.ts';

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'POST') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    const body: CreateDocumentRequest = await req.json();

    // Debug logging
    console.log('Document creation request body:', JSON.stringify(body, null, 2));
    console.log('Status from request:', body.status);

    // Validate input
    const validationErrors: ValidationError[] = [];

    if (!body.company_id) {
      validationErrors.push({ field: 'company_id', message: 'Company ID is required' });
    }

    if (!body.document_type) {
      validationErrors.push({ field: 'document_type', message: 'Document type is required' });
    } else {
      const validTypes = ['tax_invoice', 'receipt', 'credit_note', 'tax_invoice_receipt'];
      if (!validTypes.includes(body.document_type)) {
        validationErrors.push({ field: 'document_type', message: 'Invalid document type' });
      }
    }

    if (!body.customer_id) {
      validationErrors.push({ field: 'customer_id', message: 'Customer ID is required' });
    }

    if (!body.issue_date) {
      validationErrors.push({ field: 'issue_date', message: 'Issue date is required' });
    } else {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(body.issue_date)) {
        validationErrors.push({ field: 'issue_date', message: 'Issue date must be in YYYY-MM-DD format' });
      }
    }

    if (!body.currency) {
      body.currency = 'ILS';
    }

    if (!body.items || !Array.isArray(body.items) || body.items.length === 0) {
      validationErrors.push({ field: 'items', message: 'At least one item is required' });
    } else {
      body.items.forEach((item, index) => {
        if (!item.description_hebrew || item.description_hebrew.trim().length === 0) {
          validationErrors.push({ 
            field: `items[${index}].description_hebrew`, 
            message: 'Item description in Hebrew is required' 
          });
        }
        if (!item.quantity || item.quantity <= 0) {
          validationErrors.push({ 
            field: `items[${index}].quantity`, 
            message: 'Item quantity must be greater than 0' 
          });
        }
        if (!item.unit_price || item.unit_price < 0) {
          validationErrors.push({ 
            field: `items[${index}].unit_price`, 
            message: 'Item unit price must be 0 or greater' 
          });
        }
        if (item.vat_rate === undefined || item.vat_rate < 0) {
          item.vat_rate = 18; // Default Israeli VAT rate
        }
        if (item.discount_percent === undefined || item.discount_percent < 0) {
          item.discount_percent = 0;
        }
      });
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    // Check user access to company
    await checkUserRole(user.id, body.company_id, ['admin', 'user', 'accountant']);

    const supabase = createSupabaseClient();

    // Verify customer exists and belongs to company
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .select('*')
      .eq('id', body.customer_id)
      .eq('company_id', body.company_id)
      .single();

    if (customerError || !customer) {
      return errorResponse('Customer not found or does not belong to company', 404);
    }

    // Get next document number
    const { data: nextNumberData, error: numberError } = await supabase.rpc('get_next_document_number', {
      p_company_id: body.company_id,
      p_document_type: body.document_type
    });

    if (numberError) {
      console.error('Next number error:', numberError);
      return errorResponse('Failed to generate document number', 500);
    }

    const documentNumber = nextNumberData;

    // Calculate totals
    const calculations = calculateDocumentTotals(body.items);

    // Determine final status
    const finalStatus = body.status || 'pending_allocation';
    console.log('Final status for document creation:', finalStatus);

    // Create document
    const { data: document, error: documentError } = await supabase
      .from('documents')
      .insert({
        company_id: body.company_id,
        document_type: body.document_type,
        document_number: documentNumber,
        customer_id: body.customer_id,
        issue_date: body.issue_date,
        due_date: body.due_date,
        currency: body.currency || 'ILS',
        subtotal: calculations.subtotal,
        vat_amount: calculations.vat_amount,
        total_amount: calculations.total_amount,
        status: finalStatus,
        template_id: body.template_id || 'default',
        created_by: user.id,
        ita_submission_attempts: 0,
        exchange_rate: body.currency === 'ILS' ? 1.0 : 1.0, // TODO: Implement proper exchange rate
      })
      .select()
      .single();

    if (documentError) {
      console.error('Document creation error:', documentError);
      return errorResponse('Failed to create document', 500);
    }

    // Create document items
    const itemsToInsert = calculations.items.map(item => ({
      document_id: document.id,
      product_id: item.product_id,
      line_number: item.line_number,
      description_hebrew: item.description_hebrew,
      description_english: item.description_english,
      quantity: item.quantity,
      unit_price: item.unit_price,
      currency: body.currency,
      discount_percent: item.discount_percent || 0,
      vat_rate: item.vat_rate || 18,
      line_total: item.line_total,
      vat_amount: item.vat_amount,
      total_with_vat: item.total_with_vat,
    }));

    const { error: itemsError } = await supabase
      .from('document_items')
      .insert(itemsToInsert);

    if (itemsError) {
      console.error('Document items creation error:', itemsError);
      // Cleanup: delete the document
      await supabase.from('documents').delete().eq('id', document.id);
      return errorResponse('Failed to create document items', 500);
    }

    // Queue for ITA submission only if not draft
    if (document.status !== 'draft') {
      const { error: queueError } = await supabase
        .from('ita_queue')
        .insert({
          document_id: document.id,
          status: 'pending',
          request_payload: {
            document_id: document.id,
            document_type: body.document_type,
            document_number: documentNumber,
          },
          next_retry_at: new Date().toISOString(),
        });

      if (queueError) {
        console.error('ITA queue error:', queueError);
      }
    }

    // Log audit (non-blocking)
    try {
      await logAudit(
        body.company_id,
        user.id,
        'create',
        'document',
        document.id,
        null,
        document,
        req
      );
    } catch (auditError) {
      console.error('Audit logging failed (non-critical):', auditError);
      // Don't fail the entire operation if audit logging fails
    }

    // Return the document in the format expected by iOS app
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          document: document,
          itaSubmission: {
            status: document.status === 'draft' ? 'skipped' : 'queued',
            message: document.status === 'draft' ? 'Draft document - ITA submission skipped' : 'Document queued for ITA submission'
          }
        },
        error: null
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    if (error.message.includes('No access') || error.message.includes('Insufficient')) {
      return errorResponse(error.message, 403);
    }
    if (error.message.includes('Invalid token')) {
      return errorResponse('Unauthorized', 401);
    }
    return errorResponse('Internal server error', 500);
  }
});
